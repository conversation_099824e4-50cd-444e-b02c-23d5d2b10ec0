import { DBOS } from '@dbos-inc/dbos-sdk';
import { Conversation, Message, DelayedMessage, Session } from '../models/types';
import { logger } from '../utils/Logger';
import { MoodService } from './MoodService';

export class ConversationService {
  @DBOS.transaction()
  static async createConversation(): Promise<Conversation> {
    // Generate random moods for characters
    const characterMoods = MoodService.generateRandomMoods();

    const insertResult = await DBOS.knexClient<Conversation>('forachat.conversations')
      .insert({
        last_user_activity: new Date(),
        engagement_level: 1.0,
        character_moods: JSON.stringify(characterMoods)
      })
      .returning('*');

    // Handle different database drivers that may return different formats
    const conversation = Array.isArray(insertResult) ? insertResult[0] : insertResult;

    if (!conversation || !conversation.id) {
      throw new Error('Failed to create conversation - no ID returned');
    }

    logger.info(`Created new conversation ${conversation.id} with full engagement and moods: ${JSON.stringify(characterMoods)}`);
    return conversation;
  }

  @DBOS.transaction()
  static async createConversationForSession(sessionId: string): Promise<Conversation> {
    const conversation = await this.createConversation();

    // Update the session with the new conversation ID
    await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', sessionId)
      .update({
        conversation_id: conversation.id,
        updated_at: new Date()
      });

    logger.info(`Created conversation ${conversation.id} for session ${sessionId}`);
    return conversation;
  }

  @DBOS.transaction()
  static async addMessage(
    character: string,
    text: string,
    conversationId: number
  ): Promise<Message> {
    const [message] = await DBOS.knexClient<Message>('forachat.messages')
      .insert({
        character,
        text,
        conversation_id: conversationId
      })
      .returning('*');
    return message;
  }

  @DBOS.transaction()
  static async checkMessageExists(
    character: string,
    text: string,
    conversationId: number,
    timeWindowMinutes: number = 5
  ): Promise<boolean> {
    const timeWindowAgo = new Date(Date.now() - timeWindowMinutes * 60 * 1000);

    const existingMessage = await DBOS.knexClient<Message>('forachat.messages')
      .where('conversation_id', conversationId)
      .where('character', character)
      .where('text', text)
      .where('created_at', '>', timeWindowAgo)
      .first();

    return !!existingMessage;
  }

  @DBOS.transaction()
  static async getConversationMessages(conversationId: number): Promise<Message[]> {
    return DBOS.knexClient<Message>('forachat.messages')
      .where('conversation_id', conversationId)
      .orderBy('created_at', 'asc');
  }

  @DBOS.transaction()
  static async getConversation(conversationId: number): Promise<Conversation | null> {
    const conversation = await DBOS.knexClient<Conversation>('forachat.conversations')
      .where('id', conversationId)
      .first();
    return conversation || null;
  }

  @DBOS.transaction()
  static async deleteConversation(conversationId: number): Promise<void> {
    // Delete messages first due to foreign key constraint
    await DBOS.knexClient('forachat.messages')
      .where('conversation_id', conversationId)
      .del();
    
    await DBOS.knexClient('forachat.conversations')
      .where('id', conversationId)
      .del();
  }

  @DBOS.transaction()
  static async getRecentConversations(limit: number = 10): Promise<Conversation[]> {
    return DBOS.knexClient<Conversation>('forachat.conversations')
      .orderBy('updated_at', 'desc')
      .limit(limit);
  }

  @DBOS.transaction()
  static async updateMessage(messageId: number, newText: string): Promise<void> {
    await DBOS.knexClient<Message>('forachat.messages')
      .where('id', messageId)
      .update({
        text: newText,
        updated_at: new Date()
      });
  }

  @DBOS.transaction()
  static async getConversationBySession(sessionId: string): Promise<Conversation | null> {
    // First get the session to find the conversation ID
    const session = await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', sessionId)
      .first();

    if (!session || !session.conversation_id) {
      return null;
    }

    return this.getConversation(session.conversation_id);
  }

  @DBOS.transaction()
  static async getSessionsForConversation(conversationId: number): Promise<Session[]> {
    const sessions = await DBOS.knexClient<Session>('forachat.sessions')
      .where('conversation_id', conversationId)
      .orderBy('last_activity', 'desc');

    // Parse metadata for each session
    return sessions.map(session => {
      if (session.metadata && typeof session.metadata === 'string') {
        session.metadata = JSON.parse(session.metadata as string);
      }
      return session;
    });
  }

  @DBOS.transaction()
  static async getConversationWithSessions(conversationId: number): Promise<{
    conversation: Conversation | null;
    sessions: Session[];
    messages: Message[];
  }> {
    const conversation = await this.getConversation(conversationId);
    const sessions = await this.getSessionsForConversation(conversationId);
    const messages = await this.getConversationMessages(conversationId);

    return {
      conversation,
      sessions,
      messages
    };
  }

  @DBOS.transaction()
  static async transferConversationToSession(
    conversationId: number,
    fromSessionId: string,
    toSessionId: string
  ): Promise<void> {
    // Remove conversation from old session
    await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', fromSessionId)
      .update({
        conversation_id: undefined,
        updated_at: new Date()
      });

    // Add conversation to new session
    await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', toSessionId)
      .update({
        conversation_id: conversationId,
        updated_at: new Date()
      });

    logger.info(`Transferred conversation ${conversationId} from session ${fromSessionId} to session ${toSessionId}`);
  }

  @DBOS.transaction()
  static async getActiveSessionsForUser(userIdentifier: string): Promise<Session[]> {
    const sessions = await DBOS.knexClient<Session>('forachat.sessions')
      .where('user_identifier', userIdentifier)
      .where('expires_at', '>', new Date())
      .whereNotNull('conversation_id')
      .orderBy('last_activity', 'desc');

    // Parse metadata for each session
    return sessions.map(session => {
      if (session.metadata && typeof session.metadata === 'string') {
        session.metadata = JSON.parse(session.metadata as string);
      }
      return session;
    });
  }

  @DBOS.transaction()
  static async getDelayedThoughts(conversationId: number, lastMessageId?: number): Promise<DelayedMessage[]> {
    let query = DBOS.knexClient<Message>('forachat.messages')
      .where('conversation_id', conversationId)
      .where('character', '!=', 'user')
      .orderBy('id', 'asc');

    if (lastMessageId) {
      query = query.where('id', '>', lastMessageId);
    }

    const messages = await query;
    return messages.map(msg => ({
      id: msg.id,
      character: msg.character,
      text: msg.text,
      created_at: msg.created_at
    }));
  }

  @DBOS.transaction()
  static async updateUserActivity(conversationId: number): Promise<void> {
    await DBOS.knexClient<Conversation>('forachat.conversations')
      .where('id', conversationId)
      .update({
        last_user_activity: new Date(),
        engagement_level: 1.0, // Reset engagement to full when user is active
        updated_at: new Date()
      });
  }

  @DBOS.transaction()
  static async getLastUserActivity(conversationId: number): Promise<Date | null> {
    const conversation = await DBOS.knexClient<Conversation>('forachat.conversations')
      .where('id', conversationId)
      .select('last_user_activity')
      .first();

    return conversation?.last_user_activity || null;
  }

  @DBOS.transaction()
  static async updateEngagementLevel(conversationId: number, engagementLevel: number): Promise<void> {
    await DBOS.knexClient<Conversation>('forachat.conversations')
      .where('id', conversationId)
      .update({
        engagement_level: Math.max(0, Math.min(1, engagementLevel)), // Clamp between 0 and 1
        updated_at: new Date()
      });
  }

  @DBOS.transaction()
  static async updateCharacterMoods(conversationId: number, moods: any): Promise<void> {
    await DBOS.knexClient<Conversation>('forachat.conversations')
      .where('id', conversationId)
      .update({
        character_moods: JSON.stringify(moods),
        updated_at: new Date()
      });
  }

  @DBOS.transaction()
  static async updateConversationMetadata(
    conversationId: number,
    theme?: string,
    skills?: string[]
  ): Promise<void> {
    const updateData: any = {
      updated_at: new Date()
    };

    if (theme !== undefined) {
      updateData.theme = theme;
    }

    if (skills !== undefined) {
      updateData.skills = JSON.stringify(skills);
    }

    await DBOS.knexClient<Conversation>('forachat.conversations')
      .where('id', conversationId)
      .update(updateData);
  }
}
