import { DBOS } from '@dbos-inc/dbos-sdk';
import { SessionService } from './SessionService';
import { logger } from '../utils/Logger';

export class SessionCleanupService {
  private static cleanupInterval: NodeJS.Timeout | null = null;
  private static isRunning = false;

  // Start the automatic cleanup service
  static startCleanupService(intervalMinutes: number = 60): void {
    if (this.isRunning) {
      logger.warn('Session cleanup service is already running');
      return;
    }

    this.isRunning = true;
    const intervalMs = intervalMinutes * 60 * 1000;

    logger.info(`Starting session cleanup service with ${intervalMinutes} minute intervals`);

    // Run cleanup immediately on start
    this.performCleanup();

    // Schedule periodic cleanup
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, intervalMs);
  }

  // Stop the automatic cleanup service
  static stopCleanupService(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.isRunning = false;
    logger.info('Session cleanup service stopped');
  }

  // Perform the actual cleanup
  @DBOS.workflow()
  static async performCleanup(): Promise<{
    expiredSessions: number;
    orphanedConversations: number;
    oldMessages: number;
  }> {
    try {
      logger.info('Starting session cleanup...');

      // Clean up expired sessions
      const expiredSessions = await SessionCleanupService.cleanupExpiredSessionsTransaction();

      // Clean up orphaned conversations (conversations with no active sessions)
      const orphanedConversations = await SessionCleanupService.cleanupOrphanedConversationsTransaction();

      // Clean up old messages from orphaned conversations
      const oldMessages = await SessionCleanupService.cleanupOldMessagesTransaction();

      logger.info(`Session cleanup completed: ${expiredSessions} expired sessions, ${orphanedConversations} orphaned conversations, ${oldMessages} old messages`);

      return {
        expiredSessions,
        orphanedConversations,
        oldMessages
      };
    } catch (error) {
      logger.error(`Error during session cleanup`, error);
      throw error;
    }
  }

  // Clean up conversations that have no active sessions
  @DBOS.transaction()
  static async cleanupOrphanedConversations(olderThanDays: number = 7): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

    // Find conversations that have no active sessions and are older than cutoff
    // First get all conversation IDs that have active sessions
    const activeConversationIds = await DBOS.knexClient('forachat.sessions')
      .select('conversation_id')
      .where('expires_at', '>', new Date())
      .where('conversation_id', 'is not', null)
      .distinct();

    const activeIds = activeConversationIds.map(row => row.conversation_id);

    // Find conversations that are not in the active list and are older than cutoff
    let orphanedQuery = DBOS.knexClient('forachat.conversations')
      .select('id')
      .where('updated_at', '<', cutoffDate);

    if (activeIds.length > 0) {
      orphanedQuery = orphanedQuery.whereNotIn('id', activeIds);
    }

    const orphanedConversations = await orphanedQuery;

    const conversationIds = orphanedConversations.map((row: any) => row.id);

    if (conversationIds.length === 0) {
      return 0;
    }

    // Delete messages from orphaned conversations first (due to foreign key constraints)
    await DBOS.knexClient('forachat.messages')
      .whereIn('conversation_id', conversationIds)
      .del();

    // Delete the orphaned conversations
    const deletedCount = await DBOS.knexClient('forachat.conversations')
      .whereIn('id', conversationIds)
      .del();

    logger.info(`Cleaned up ${deletedCount} orphaned conversations older than ${olderThanDays} days`);
    return deletedCount;
  }

  // Clean up old messages from conversations
  @DBOS.transaction()
  static async cleanupOldMessages(olderThanDays: number = 30, keepRecentCount: number = 50): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

    // For each conversation, keep only the most recent messages and delete older ones
    const conversations = await DBOS.knexClient('forachat.conversations')
      .select('id')
      .where('updated_at', '<', cutoffDate);

    let totalDeleted = 0;

    for (const conversation of conversations) {
      // Get message IDs to keep (most recent ones)
      const messagesToKeep = await DBOS.knexClient('forachat.messages')
        .select('id')
        .where('conversation_id', conversation.id)
        .orderBy('created_at', 'desc')
        .limit(keepRecentCount);

      const keepIds = messagesToKeep.map(msg => msg.id);

      if (keepIds.length === 0) {
        // If no messages to keep, delete all messages for this conversation
        const deleted = await DBOS.knexClient('forachat.messages')
          .where('conversation_id', conversation.id)
          .del();
        totalDeleted += deleted;
      } else {
        // Delete old messages, keeping the recent ones
        const deleted = await DBOS.knexClient('forachat.messages')
          .where('conversation_id', conversation.id)
          .whereNotIn('id', keepIds)
          .where('created_at', '<', cutoffDate)
          .del();
        totalDeleted += deleted;
      }
    }

    if (totalDeleted > 0) {
      logger.info(`Cleaned up ${totalDeleted} old messages older than ${olderThanDays} days`);
    }

    return totalDeleted;
  }

  // Clean up message queue entries
  @DBOS.transaction()
  static async cleanupMessageQueue(olderThanHours: number = 24): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);

    const deletedCount = await DBOS.knexClient('forachat.message_queue')
      .where('created_at', '<', cutoffDate)
      .whereIn('status', ['SENT', 'CANCELLED'])
      .del();

    if (deletedCount > 0) {
      logger.info(`Cleaned up ${deletedCount} old message queue entries`);
    }

    return deletedCount;
  }

  // Get cleanup statistics
  @DBOS.transaction()
  static async getCleanupStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
    totalConversations: number;
    orphanedConversations: number;
    totalMessages: number;
    oldMessages: number;
  }> {
    const now = new Date();

    const [
      totalSessions,
      activeSessions,
      totalConversations,
      orphanedConversations,
      totalMessages,
      oldMessages
    ] = await Promise.all([
      DBOS.knexClient('forachat.sessions').count('* as count').first(),
      DBOS.knexClient('forachat.sessions').where('expires_at', '>', now).count('* as count').first(),
      DBOS.knexClient('forachat.conversations').count('* as count').first(),
      // Count orphaned conversations using the same logic as cleanup
      (async () => {
        const activeConversationIds = await DBOS.knexClient('forachat.sessions')
          .select('conversation_id')
          .where('expires_at', '>', new Date())
          .where('conversation_id', 'is not', null)
          .distinct();

        const activeIds = activeConversationIds.map(row => row.conversation_id);

        let orphanedQuery = DBOS.knexClient('forachat.conversations')
          .count('* as count');

        if (activeIds.length > 0) {
          orphanedQuery = orphanedQuery.whereNotIn('id', activeIds);
        }

        const result = await orphanedQuery.first();
        return result;
      })(),
      DBOS.knexClient('forachat.messages').count('* as count').first(),
      DBOS.knexClient('forachat.messages')
        .where('created_at', '<', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
        .count('* as count').first()
    ]);

    return {
      totalSessions: parseInt(String(totalSessions?.count || '0')),
      activeSessions: parseInt(String(activeSessions?.count || '0')),
      expiredSessions: parseInt(String(totalSessions?.count || '0')) - parseInt(String(activeSessions?.count || '0')),
      totalConversations: parseInt(String(totalConversations?.count || '0')),
      orphanedConversations: parseInt(String(orphanedConversations?.count || '0')),
      totalMessages: parseInt(String(totalMessages?.count || '0')),
      oldMessages: parseInt(String(oldMessages?.count || '0'))
    };
  }

  // Manual cleanup with custom parameters
  @DBOS.workflow()
  static async manualCleanup(options: {
    cleanupExpiredSessions?: boolean;
    cleanupOrphanedConversations?: boolean;
    orphanedConversationDays?: number;
    cleanupOldMessages?: boolean;
    oldMessageDays?: number;
    keepRecentMessageCount?: number;
    cleanupMessageQueue?: boolean;
    messageQueueHours?: number;
  } = {}): Promise<{
    expiredSessions: number;
    orphanedConversations: number;
    oldMessages: number;
    messageQueueEntries: number;
  }> {
    const {
      cleanupExpiredSessions = true,
      cleanupOrphanedConversations = true,
      orphanedConversationDays = 7,
      cleanupOldMessages = true,
      oldMessageDays = 30,
      keepRecentMessageCount = 50,
      cleanupMessageQueue = true,
      messageQueueHours = 24
    } = options;

    let expiredSessions = 0;
    let orphanedConversations = 0;
    let oldMessages = 0;
    let messageQueueEntries = 0;

    if (cleanupExpiredSessions) {
      expiredSessions = await SessionCleanupService.cleanupExpiredSessionsTransaction();
    }

    if (cleanupOrphanedConversations) {
      orphanedConversations = await SessionCleanupService.cleanupOrphanedConversationsTransaction(orphanedConversationDays);
    }

    if (cleanupOldMessages) {
      oldMessages = await SessionCleanupService.cleanupOldMessagesTransaction(oldMessageDays, keepRecentMessageCount);
    }

    if (cleanupMessageQueue) {
      messageQueueEntries = await SessionCleanupService.cleanupMessageQueueTransaction(messageQueueHours);
    }

    return {
      expiredSessions,
      orphanedConversations,
      oldMessages,
      messageQueueEntries
    };
  }

  // ===== STEP WRAPPERS FOR WORKFLOW CALLS =====

  /**
   * Transaction for cleaning up expired sessions (for workflow calls)
   */
  @DBOS.transaction()
  static async cleanupExpiredSessionsTransaction(): Promise<number> {
    const deletedCount = await DBOS.knexClient('forachat.sessions')
      .where('expires_at', '<', new Date())
      .del();

    if (deletedCount > 0) {
      logger.info(`Cleaned up ${deletedCount} expired sessions`);
    }

    return deletedCount;
  }



  /**
   * Transaction for cleaning up orphaned conversations (for workflow calls)
   */
  @DBOS.transaction()
  static async cleanupOrphanedConversationsTransaction(olderThanDays: number = 7): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

    // Find conversations that have no active sessions and are older than cutoff
    const activeConversationIds = await DBOS.knexClient('forachat.sessions')
      .select('conversation_id')
      .where('expires_at', '>', new Date())
      .where('conversation_id', 'is not', null)
      .distinct();

    const activeIds = activeConversationIds.map(row => row.conversation_id);

    // Find conversations that are not in the active list and are older than cutoff
    let orphanedQuery = DBOS.knexClient('forachat.conversations')
      .select('id')
      .where('updated_at', '<', cutoffDate);

    if (activeIds.length > 0) {
      orphanedQuery = orphanedQuery.whereNotIn('id', activeIds);
    }

    const orphanedConversations = await orphanedQuery;
    const conversationIds = orphanedConversations.map((row: any) => row.id);

    if (conversationIds.length === 0) {
      return 0;
    }

    // Delete messages from orphaned conversations first (due to foreign key constraints)
    await DBOS.knexClient('forachat.messages')
      .whereIn('conversation_id', conversationIds)
      .del();

    // Delete the orphaned conversations
    const deletedCount = await DBOS.knexClient('forachat.conversations')
      .whereIn('id', conversationIds)
      .del();

    logger.info(`Cleaned up ${deletedCount} orphaned conversations older than ${olderThanDays} days`);
    return deletedCount;
  }



  /**
   * Transaction for cleaning up old messages (for workflow calls)
   */
  @DBOS.transaction()
  static async cleanupOldMessagesTransaction(olderThanDays: number = 30, keepRecentCount: number = 50): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

    // For each conversation, keep only the most recent messages and delete older ones
    const conversations = await DBOS.knexClient('forachat.conversations')
      .select('id')
      .where('updated_at', '<', cutoffDate);

    let totalDeleted = 0;

    for (const conversation of conversations) {
      // Get message IDs to keep (most recent ones)
      const messagesToKeep = await DBOS.knexClient('forachat.messages')
        .select('id')
        .where('conversation_id', conversation.id)
        .orderBy('created_at', 'desc')
        .limit(keepRecentCount);

      const keepIds = messagesToKeep.map(msg => msg.id);

      if (keepIds.length === 0) {
        // If no messages to keep, delete all messages for this conversation
        const deleted = await DBOS.knexClient('forachat.messages')
          .where('conversation_id', conversation.id)
          .del();
        totalDeleted += deleted;
      } else {
        // Delete old messages, keeping the recent ones
        const deleted = await DBOS.knexClient('forachat.messages')
          .where('conversation_id', conversation.id)
          .whereNotIn('id', keepIds)
          .where('created_at', '<', cutoffDate)
          .del();
        totalDeleted += deleted;
      }
    }

    if (totalDeleted > 0) {
      logger.info(`Cleaned up ${totalDeleted} old messages older than ${olderThanDays} days`);
    }

    return totalDeleted;
  }



  /**
   * Transaction for cleaning up message queue (for workflow calls)
   */
  @DBOS.transaction()
  static async cleanupMessageQueueTransaction(olderThanHours: number = 24): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);

    const deletedCount = await DBOS.knexClient('forachat.message_queue')
      .where('created_at', '<', cutoffDate)
      .whereIn('status', ['SENT', 'CANCELLED'])
      .del();

    if (deletedCount > 0) {
      logger.info(`Cleaned up ${deletedCount} old message queue entries`);
    }

    return deletedCount;
  }


}
