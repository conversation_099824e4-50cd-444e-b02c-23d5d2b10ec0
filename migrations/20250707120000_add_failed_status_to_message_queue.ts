exports.up = function(knex) {
    return knex.raw(`
        ALTER TYPE forachat.message_queue_status_enum 
        ADD VALUE IF NOT EXISTS 'FAILED'
    `).catch(() => {
        // If the enum doesn't exist as a custom type, alter the table directly
        return knex.schema
            .withSchema('forachat')
            .alterTable('message_queue', (table) => {
                table.dropColumn('status');
            })
            .then(() => {
                return knex.schema
                    .withSchema('forachat')
                    .alterTable('message_queue', (table) => {
                        table.enum('status', ['PENDING', 'PROCESSING', 'VALIDATING', 'SENT', 'CANCELLED', 'WITHDRAWN', 'FAILED']).defaultTo('PENDING').notNullable();
                    });
            });
    });
};

exports.down = function(knex) {
    return knex.schema
        .withSchema('forachat')
        .alterTable('message_queue', (table) => {
            table.dropColumn('status');
        })
        .then(() => {
            return knex.schema
                .withSchema('forachat')
                .alterTable('message_queue', (table) => {
                    table.enum('status', ['PENDING', 'PROCESSING', 'VALIDATING', 'SENT', 'CANCELLED', 'WITHDRAWN']).defaultTo('PENDING').notNullable();
                });
        });
};
