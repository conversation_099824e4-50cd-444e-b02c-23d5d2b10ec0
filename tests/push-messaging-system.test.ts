import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { NotificationDispatcher, NotificationTarget, NotificationMessage } from '../src/streaming/NotificationDispatcher';
import { EventDrivenMessageProcessor } from '../src/streaming/EventDrivenMessageProcessor';
import { MessageValidationService } from '../src/core/MessageValidationService';
import { SSEManager } from '../src/streaming/SSEManager';
import { QueuedMessage, ValidationContext, MessageQueueStatus } from '../src/models/types';

// Mock dependencies
jest.mock('../src/utils/Logger');
jest.mock('../src/core/MessageQueueService');
jest.mock('../src/core/ConversationService');
jest.mock('../src/core/MessageValidationService', () => ({
  MessageValidationService: {
    buildValidationContext: jest.fn(),
    getCharacterValidationDecision: jest.fn(),
    applyValidationResult: jest.fn(),
    validateQueuedMessage: jest.fn(),
    getValidationStats: jest.fn(),
    validateMultipleMessages: jest.fn(),
  }
}));

describe('Push-Based Messaging System', () => {
  let notificationDispatcher: NotificationDispatcher;
  let eventProcessor: EventDrivenMessageProcessor;
  let sseManager: SSEManager;

  beforeEach(() => {
    // Reset singletons
    (NotificationDispatcher as any).instance = undefined;
    (EventDrivenMessageProcessor as any).instance = undefined;
    (SSEManager as any).instance = undefined;

    notificationDispatcher = NotificationDispatcher.getInstance();
    eventProcessor = EventDrivenMessageProcessor.getInstance();
    sseManager = SSEManager.getInstance();
  });

  afterEach(() => {
    // Clean up
    jest.clearAllMocks();
  });

  describe('NotificationDispatcher', () => {
    it('should register and manage clients correctly', () => {
      const mockWs = { readyState: 1, send: jest.fn() };
      
      const target: NotificationTarget = {
        type: 'websocket',
        sessionId: 'test-session-1',
        conversationId: 123,
        connection: mockWs
      };

      notificationDispatcher.registerClient(target);
      
      const activeClients = notificationDispatcher.getActiveClients();
      expect(activeClients).toHaveLength(1);
      expect(activeClients[0].sessionId).toBe('test-session-1');
      expect(activeClients[0].types).toContain('websocket');
    });

    it('should deliver notifications to WebSocket clients', async () => {
      const mockWs = { readyState: 1, send: jest.fn() };
      
      const target: NotificationTarget = {
        type: 'websocket',
        sessionId: 'test-session-1',
        connection: mockWs
      };

      notificationDispatcher.registerClient(target);

      const message: NotificationMessage = {
        id: 'test-msg-1',
        type: 'message',
        data: { character: 'Fora', text: 'Hello world!' },
        timestamp: new Date(),
        priority: 1
      };

      const results = await notificationDispatcher.notifySession('test-session-1', message);
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(true);
      expect(mockWs.send).toHaveBeenCalledWith(
        JSON.stringify({
          id: 'test-msg-1',
          type: 'message',
          timestamp: message.timestamp,
          character: 'Fora',
          text: 'Hello world!'
        })
      );
    });

    it('should deliver notifications to conversation participants', async () => {
      const mockWs1 = { readyState: 1, send: jest.fn() };
      const mockWs2 = { readyState: 1, send: jest.fn() };
      
      const target1: NotificationTarget = {
        type: 'websocket',
        sessionId: 'session-1',
        conversationId: 123,
        connection: mockWs1
      };

      const target2: NotificationTarget = {
        type: 'websocket',
        sessionId: 'session-2',
        conversationId: 123,
        connection: mockWs2
      };

      notificationDispatcher.registerClient(target1);
      notificationDispatcher.registerClient(target2);

      const message: NotificationMessage = {
        id: 'test-msg-1',
        type: 'message',
        data: { character: 'Jan', text: 'Team message!' },
        timestamp: new Date(),
        priority: 1,
        conversationId: 123
      };

      const results = await notificationDispatcher.notifyConversation(123, message);
      
      expect(results).toHaveLength(2);
      expect(results.every(r => r.success)).toBe(true);
      expect(mockWs1.send).toHaveBeenCalled();
      expect(mockWs2.send).toHaveBeenCalled();
    });

    it('should handle SSE client registration', () => {
      const mockRes = { write: jest.fn(), destroyed: false };
      
      const target: NotificationTarget = {
        type: 'sse',
        sessionId: 'sse-session-1',
        connection: mockRes
      };

      notificationDispatcher.registerClient(target);
      
      const activeClients = notificationDispatcher.getActiveClients();
      expect(activeClients).toHaveLength(1);
      expect(activeClients[0].types).toContain('sse');
    });

    it('should handle REPL client registration', () => {
      const mockHandler = { handleNotification: jest.fn() };
      
      const target: NotificationTarget = {
        type: 'repl',
        sessionId: 'repl-session-1',
        connection: mockHandler
      };

      notificationDispatcher.registerClient(target);
      
      const activeClients = notificationDispatcher.getActiveClients();
      expect(activeClients).toHaveLength(1);
      expect(activeClients[0].types).toContain('repl');
    });
  });

  describe('EventDrivenMessageProcessor', () => {
    it('should start and stop conversation processing', () => {
      const conversationId = 456;
      
      eventProcessor.startConversationProcessing(conversationId);
      
      const stats = eventProcessor.getProcessingStats();
      expect(stats.activeConversations).toBe(1);
      
      eventProcessor.stopConversationProcessing(conversationId);
      
      const statsAfter = eventProcessor.getProcessingStats();
      expect(statsAfter.activeConversations).toBe(0);
    });

    it('should emit and handle message events', () => {
      const conversationId = 789;
      const mockHandler = jest.fn();
      
      eventProcessor.on('message_queued', mockHandler);
      
      eventProcessor.emitMessageEvent({
        type: 'queued',
        messageId: 1,
        conversationId,
        character: 'Lou',
        timestamp: new Date()
      });
      
      expect(mockHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'queued',
          messageId: 1,
          character: 'Lou'
        })
      );
    });
  });

  describe('Message Validation Integration', () => {
    it('should notify clients when validation starts', async () => {
      const mockMessage: QueuedMessage = {
        id: 1,
        character: 'Fora',
        text: 'Original message',
        conversation_id: 123,
        delay_ms: 1000,
        status: 'PENDING' as MessageQueueStatus,
        priority: 1,
        created_at: new Date(),
        updated_at: new Date()
      };

      // Mock the validation service methods
      const mockValidationService = MessageValidationService as jest.Mocked<typeof MessageValidationService>;
      mockValidationService.buildValidationContext.mockResolvedValue({
        originalMessage: mockMessage,
        conversationHistory: [],
        characterMoods: null
      } as ValidationContext);

      const mockWs = { readyState: 1, send: jest.fn() };
      const target: NotificationTarget = {
        type: 'websocket',
        sessionId: 'test-session',
        conversationId: 123,
        connection: mockWs
      };

      notificationDispatcher.registerClient(target);

      // The validation service should send notifications
      // This would be tested by mocking the notification calls
      expect(notificationDispatcher.getActiveClients()).toHaveLength(1);
    });
  });

  describe('Multi-Client Support', () => {
    it('should handle multiple client types for the same session', async () => {
      const sessionId = 'multi-client-session';
      const mockWs = { readyState: 1, send: jest.fn() };
      const mockRes = { write: jest.fn(), destroyed: false };
      const mockHandler = { handleNotification: jest.fn() };

      // Register multiple client types for the same session
      notificationDispatcher.registerClient({
        type: 'websocket',
        sessionId,
        connection: mockWs
      });

      notificationDispatcher.registerClient({
        type: 'sse',
        sessionId,
        connection: mockRes
      });

      notificationDispatcher.registerClient({
        type: 'repl',
        sessionId,
        connection: mockHandler
      });

      const message: NotificationMessage = {
        id: 'multi-msg-1',
        type: 'system_notification',
        data: { event: 'test' },
        timestamp: new Date(),
        priority: 1
      };

      const results = await notificationDispatcher.notifySession(sessionId, message);
      
      expect(results).toHaveLength(3);
      expect(results.every(r => r.success)).toBe(true);
      expect(mockWs.send).toHaveBeenCalled();
      expect(mockRes.write).toHaveBeenCalled();
      expect(mockHandler.handleNotification).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle WebSocket send errors gracefully', async () => {
      const mockWs = { 
        readyState: 1, 
        send: jest.fn().mockImplementation(() => {
          throw new Error('WebSocket send failed');
        })
      };
      
      const target: NotificationTarget = {
        type: 'websocket',
        sessionId: 'error-session',
        connection: mockWs
      };

      notificationDispatcher.registerClient(target);

      const message: NotificationMessage = {
        id: 'error-msg-1',
        type: 'message',
        data: { character: 'Fora', text: 'Test message' },
        timestamp: new Date(),
        priority: 1
      };

      const results = await notificationDispatcher.notifySession('error-session', message);
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(false);
      expect(results[0].error).toBe('WebSocket send failed');
    });

    it('should handle closed WebSocket connections', async () => {
      const mockWs = { readyState: 3, send: jest.fn() }; // CLOSED state
      
      const target: NotificationTarget = {
        type: 'websocket',
        sessionId: 'closed-session',
        connection: mockWs
      };

      notificationDispatcher.registerClient(target);

      const message: NotificationMessage = {
        id: 'closed-msg-1',
        type: 'message',
        data: { character: 'Jan', text: 'Test message' },
        timestamp: new Date(),
        priority: 1
      };

      const results = await notificationDispatcher.notifySession('closed-session', message);
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(false);
      expect(mockWs.send).not.toHaveBeenCalled();
    });
  });
});
